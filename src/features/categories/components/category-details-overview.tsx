import type { Category } from "../types";

import { formatDate } from "date-fns";
import { BanknoteArrowDownIcon, BanknoteArrowUpIcon, Edit2Icon, MoreHorizontalIcon, Trash2Icon } from "lucide-react";

import { Box } from "~/components/blocks";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "~/components/ui/dropdown-menu";

import { defaultColor } from "../constants";
import { useCategoryActions } from "../hooks";
import CategoryIcon from "./category-icon";

interface Props {
  category: Category;
}

export default function CategoryDetailsOverview({ category }: Props) {
  const { editCategory, deleteCategory } = useCategoryActions();

  return (
    <Box
      className="flex flex-col gap-6 border-t-[6px] ps-8 pe-4 pt-4 pb-8"
      style={{ borderColor: category.color || defaultColor }}
    >
      <div className="flex items-center gap-4">
        <CategoryIcon category={category} className="text-gray size-4" />
        <p className="text-foreground text-xs/5 uppercase" title="Created at">
          {formatDate(category.createdAt, "PP")}
        </p>
        {category.isExpense && (
          <p className="text-red flex items-center gap-1 text-sm/5">
            <BanknoteArrowDownIcon className="size-4" />
            Expense
          </p>
        )}
        {!category.isExpense && (
          <p className="text-green flex items-center gap-1 text-sm/5">
            <BanknoteArrowUpIcon className="size-4" />
            Income
          </p>
        )}

        <DropdownMenu>
          <DropdownMenuTrigger className="ms-auto">
            <MoreHorizontalIcon className="text-gray hover:bg-gray/5 size-5 cursor-pointer rounded-xs" />
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={() => editCategory(category)}>
              <Edit2Icon className="mr-1 inline-block size-3" />
              Edit category
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => deleteCategory(category)} variant="destructive">
              <Trash2Icon className="mr-1 inline-block size-3" />
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      <div>
        <h2 className="text-foreground text-3xl/8 font-bold">{category.name}</h2>
      </div>
    </Box>
  );
}
