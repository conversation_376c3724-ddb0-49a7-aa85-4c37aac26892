import { useQuery } from "@tanstack/react-query";

import { getTransactionOptions } from "~/api/@tanstack/react-query.gen";

export function useTransaction(transactionId: string) {
  const {
    data: transaction,
    isLoading,
    error,
    refetch,
  } = useQuery(
    getTransactionOptions({
      path: {
        id: transactionId,
      },
    })
  );

  return {
    transaction,
    isLoading,
    error,
    refetch,
  };
}
