import type { TransactionCreateRequest, TransactionResponse, TransactionUpdateRequest } from "~/api/types.gen";

import { useCreateTransaction } from "./use-create-transaction";
import { useDeleteTransaction } from "./use-delete-transaction";
import { useUpdateTransaction } from "./use-update-transaction";
import { useTransactionActionsStore } from "../store";

/**
 * Main hook that provides all transaction CRUD operations and dialog actions.
 * This is the primary interface for transaction management throughout the app.
 */
export function useTransactionActions() {
  const {
    openCreateTransactionDialog,
    openEditTransactionDialog,
    closeTransactionDialog,
  } = useTransactionActionsStore();

  const { createTransaction, isLoading: isCreating } = useCreateTransaction();
  const { updateTransaction, isLoading: isUpdating } = useUpdateTransaction();
  const { deleteTransaction, isLoading: isDeleting } = useDeleteTransaction();

  const handleCreateTransaction = (data: TransactionCreateRequest) => {
    createTransaction(data);
  };

  const handleUpdateTransaction = (transactionId: string, data: TransactionUpdateRequest) => {
    updateTransaction(transactionId, data);
  };

  const handleDeleteTransaction = (transaction: TransactionResponse) => {
    deleteTransaction(transaction);
  };

  const handleEditTransaction = (transaction: TransactionResponse) => {
    openEditTransactionDialog(transaction);
  };

  return {
    // Dialog actions
    openCreateTransactionDialog,
    openEditTransactionDialog,
    closeTransactionDialog,

    // CRUD operations
    createTransaction: handleCreateTransaction,
    updateTransaction: handleUpdateTransaction,
    deleteTransaction: handleDeleteTransaction,
    editTransaction: handleEditTransaction,

    // Loading states
    isCreating,
    isUpdating,
    isDeleting,
    isLoading: isCreating || isUpdating || isDeleting,
  };
}
