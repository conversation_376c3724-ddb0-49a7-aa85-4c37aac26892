import { useMemo } from "react";

import { useQuery } from "@tanstack/react-query";

import { listTransactionsOptions } from "~/api/@tanstack/react-query.gen";

interface UseTransactionsOptions {
  page?: number;
  limit?: number;
}

export function useTransactions(options: UseTransactionsOptions = {}) {
  const { page = 1, limit = 20 } = options;

  const {
    data: transactionsData,
    isLoading,
    error,
    refetch,
  } = useQuery(
    listTransactionsOptions({
      query: { page, limit },
    })
  );

  const transactions = useMemo(() => transactionsData?.items || [], [transactionsData]);
  const meta = useMemo(() => transactionsData?.meta, [transactionsData]);
  const totalPages = useMemo(() => (meta ? Math.ceil(meta.total / meta.limit) : 0), [meta]);

  return { transactions, meta, totalPages, isLoading, error, refetch };
}
