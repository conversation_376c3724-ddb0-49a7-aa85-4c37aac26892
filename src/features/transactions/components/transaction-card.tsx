import type { TransactionResponse } from "~/api/types.gen";

import { Edit2Icon, MoreHorizontalIcon, Trash2Icon } from "lucide-react";
import { Link } from "@tanstack/react-router";

import { Badge } from "~/components/ui/badge";
import { But<PERSON> } from "~/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "~/components/ui/dropdown-menu";
import { useConfirm } from "~/features/ui/confirmations/hooks";

import { useDeleteTransaction } from "../hooks";
import { useTransactionActionsStore } from "../store";
import {
  formatTransactionAmount,
  formatTransactionDate,
  getTransactionAccountText,
  getTransactionCategoryText,
  getTransactionDescription,
} from "../utils";
import { getTransactionTypeColor, getTransactionTypeIcon, getTransactionTypeLabel } from "../utils";

interface Props {
  transaction: TransactionResponse;
}

export default function TransactionCard({ transaction }: Props) {
  const { openEditTransactionDialog } = useTransactionActionsStore();
  const { deleteTransaction, isLoading: isDeleting } = useDeleteTransaction();
  const confirm = useConfirm();

  const handleEdit = () => {
    openEditTransactionDialog(transaction);
  };

  const handleDelete = async () => {
    const confirmed = await confirm({
      title: "Delete Transaction",
      description: `Are you sure you want to delete this ${transaction.type} transaction? This action cannot be undone.`,
      confirmText: "Delete",
      cancelText: "Cancel",
    });

    if (confirmed) {
      deleteTransaction(transaction);
    }
  };

  const TypeIcon = getTransactionTypeIcon(transaction.type);
  const typeColor = getTransactionTypeColor(transaction.type);
  const typeLabel = getTransactionTypeLabel(transaction.type);
  const accountText = getTransactionAccountText(transaction);
  const categoryText = getTransactionCategoryText(transaction);
  const description = getTransactionDescription(transaction);
  const formattedAmount = formatTransactionAmount(
    transaction.amount,
    transaction.account.currency,
    transaction.type
  );
  const formattedDate = formatTransactionDate(transaction.transactionDate);

  return (
    <div className="border-input flex items-center justify-between rounded-lg border bg-white p-4 transition-colors hover:bg-gray-50">
      {/* Left side - Transaction info */}
      <div className="flex items-center gap-4">
        {/* Type icon */}
        <div className={`flex h-10 w-10 items-center justify-center rounded-full bg-gray-100 ${typeColor}`}>
          <TypeIcon className="h-5 w-5" />
        </div>

        {/* Transaction details */}
        <div className="flex flex-col">
          <div className="flex items-center gap-2">
            <span className="font-medium text-gray-900">{description}</span>
            <Badge variant="outline" className="text-xs">
              {typeLabel}
            </Badge>
          </div>
          
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <span>{accountText}</span>
            {categoryText && (
              <>
                <span>•</span>
                <span>{categoryText}</span>
              </>
            )}
            <span>•</span>
            <span>{formattedDate}</span>
          </div>
        </div>
      </div>

      {/* Right side - Amount and actions */}
      <div className="flex items-center gap-4">
        {/* Amount */}
        <div className="text-right">
          <div className={`font-semibold ${typeColor}`}>
            {formattedAmount}
          </div>
          {transaction.type === "transfer" && transaction.amountTo && (
            <div className="text-sm text-gray-600">
              → {formatTransactionAmount(
                transaction.amountTo,
                transaction.accountTo?.currency || transaction.account.currency,
                "transfer",
                { showSign: false }
              )}
            </div>
          )}
        </div>

        {/* Actions dropdown */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
              <MoreHorizontalIcon className="h-4 w-4" />
              <span className="sr-only">Open menu</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem asChild>
              <Link to="/transactions/$transactionId" params={{ transactionId: transaction.id }}>
                View Details
              </Link>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={handleEdit}>
              <Edit2Icon className="mr-2 h-4 w-4" />
              Edit
            </DropdownMenuItem>
            <DropdownMenuItem onClick={handleDelete} disabled={isDeleting} className="text-red-600">
              <Trash2Icon className="mr-2 h-4 w-4" />
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  );
}
