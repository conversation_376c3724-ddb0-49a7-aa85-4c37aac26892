import { TrendingUpIcon, TrendingDownIcon, ArrowRightLeftIcon, type LucideIcon } from "lucide-react";

import type { TransactionType } from "~/api/types.gen";

/**
 * Maps transaction types to their corresponding Lucide React icons
 */
export const TRANSACTION_TYPE_ICONS: Record<TransactionType, LucideIcon> = {
  income: TrendingUpIcon,
  expense: TrendingDownIcon,
  transfer: ArrowRightLeftIcon,
};

/**
 * Gets the appropriate icon component for a transaction type
 */
export function getTransactionTypeIcon(type: TransactionType): LucideIcon {
  return TRANSACTION_TYPE_ICONS[type];
}

/**
 * Gets the emoji icon for a transaction type
 */
export function getTransactionTypeEmoji(type: TransactionType): string {
  const emojiMap: Record<TransactionType, string> = {
    income: "📈",
    expense: "📉",
    transfer: "🔄",
  };
  return emojiMap[type];
}

/**
 * Gets the display label for a transaction type
 */
export function getTransactionTypeLabel(type: TransactionType): string {
  const labelMap: Record<TransactionType, string> = {
    income: "Income",
    expense: "Expense",
    transfer: "Transfer",
  };
  return labelMap[type];
}

/**
 * Gets the color class for a transaction type
 */
export function getTransactionTypeColor(type: TransactionType): string {
  const colorMap: Record<TransactionType, string> = {
    income: "text-green-600",
    expense: "text-red-600",
    transfer: "text-blue-600",
  };
  return colorMap[type];
}
