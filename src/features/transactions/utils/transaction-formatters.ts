import type { TransactionResponse, TransactionType } from "~/api/types.gen";

import { formatCurrency } from "~/lib/formatters";

/**
 * Formats a transaction amount with appropriate sign and styling
 */
export function formatTransactionAmount(
  amount: string,
  currency: string,
  type: TransactionType,
  options?: {
    showSign?: boolean;
    showCurrency?: boolean;
  }
): string {
  const { showSign = true, showCurrency = true } = options || {};
  
  let formattedAmount = formatCurrency(amount, currency);
  
  if (!showCurrency) {
    // Remove currency symbol for display
    formattedAmount = formattedAmount.replace(/[^\d.,\s-]/g, "").trim();
  }
  
  if (showSign && type === "expense") {
    formattedAmount = `-${formattedAmount}`;
  } else if (showSign && type === "income") {
    formattedAmount = `+${formattedAmount}`;
  }
  
  return formattedAmount;
}

/**
 * Gets the display description for a transaction
 */
export function getTransactionDescription(transaction: TransactionResponse): string {
  return transaction.description || "No description";
}

/**
 * Gets the account display text for a transaction
 */
export function getTransactionAccountText(transaction: TransactionResponse): string {
  if (transaction.type === "transfer" && transaction.accountTo) {
    return `${transaction.account.name} → ${transaction.accountTo.name}`;
  }
  return transaction.account.name;
}

/**
 * Formats a transaction date for display
 */
export function formatTransactionDate(dateString: string): string {
  const date = new Date(dateString);
  return date.toLocaleDateString(undefined, {
    year: "numeric",
    month: "short",
    day: "numeric",
  });
}

/**
 * Gets the category display text for a transaction
 */
export function getTransactionCategoryText(transaction: TransactionResponse): string | null {
  return transaction.category?.name || null;
}
