import type { TransactionType } from "~/api/types.gen";

// Transaction type options for forms
export const transactionTypeOptions = [
  { value: "income" as TransactionType, label: "Income", icon: "📈", color: "text-green-600" },
  { value: "expense" as TransactionType, label: "Expense", icon: "📉", color: "text-red-600" },
  { value: "transfer" as TransactionType, label: "Transfer", icon: "🔄", color: "text-blue-600" },
] as const;

// Transaction type labels
export const transactionTypeLabels: Record<TransactionType, string> = {
  income: "Income",
  expense: "Expense",
  transfer: "Transfer",
};

// Transaction type icons
export const transactionTypeIcons: Record<TransactionType, string> = {
  income: "📈",
  expense: "📉",
  transfer: "🔄",
};

// Transaction type colors for styling
export const transactionTypeColors: Record<TransactionType, string> = {
  income: "text-green-600",
  expense: "text-red-600",
  transfer: "text-blue-600",
};

// Default pagination settings
export const DEFAULT_PAGE_SIZE = 20;
export const MAX_PAGE_SIZE = 100;
